﻿/*
 * Function: ?Send@CWeeklyGuildRankInfo@@QEAAXKHEEK@Z
 * Address: 0x1402CAFD0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CWeeklyGuildRankInfo::Send(CWeeklyGuildRankInfo *this, unsigned int dwVer, int n, char byTabRace, char bySelfRace, unsigned int dwGuildSerial)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v8; // ax@16
  __int64 v9; // [sp+0h] [bp-B8h]@1
  unsigned __int16 nLen[2]; // [sp+20h] [bp-98h]@9
  unsigned int v11; // [sp+28h] [bp-90h]@9
  unsigned int v12; // [sp+30h] [bp-88h]@9
  int v13; // [sp+38h] [bp-80h]@9
  unsigned int v14; // [sp+40h] [bp-78h]@9
  char szMsg[4]; // [sp+54h] [bp-64h]@12
  char v16; // [sp+58h] [bp-60h]@12
  char pbyType; // [sp+74h] [bp-44h]@12
  char v18; // [sp+75h] [bp-43h]@12
  int v19; // [sp+84h] [bp-34h]@13
  char v20; // [sp+94h] [bp-24h]@16
  char v21; // [sp+95h] [bp-23h]@16
  CWeeklyGuildRankInfo *v22; // [sp+C0h] [bp+8h]@1
  unsigned int v23; // [sp+C8h] [bp+10h]@1
  int dwClientIndex; // [sp+D0h] [bp+18h]@1
  char v25; // [sp+D8h] [bp+20h]@1

  v25 = byTabRace;
  dwClientIndex = n;
  v23 = dwVer;
  v22 = this;
  v6 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( v22->m_bInit && (signed int)(unsigned __int8)byTabRace < 3 && (signed int)(unsigned __int8)bySelfRace < 3 )
  {
    if ( dwVer == v22->m_pkSendList[(unsigned __int8)byTabRace].dwVer )
    {
      v14 = v22->m_pkSendList[(unsigned __int8)byTabRace].dwVer;
      v13 = (unsigned __int8)byTabRace;
      v12 = dwVer;
      v11 = dwGuildSerial;
      *(_DWORD *)nLen = (unsigned __int8)bySelfRace;
      __trace(
        "CWeeklyGuildRankInfo::Send( %u, %d, %u, %u, %u ) : %u == m_pkSendList[%u].dwVer(%u)",
        dwVer,
        (unsigned int)n);
    }
    else if ( !v22->m_bNoDataToday && v22->m_pkSendList[(unsigned __int8)byTabRace].byCnt )
    {
      v19 = CWeeklyGuildRankInfo::Find(v22, bySelfRace, dwGuildSerial);
      v12 = v19;
      v11 = dwGuildSerial;
      *(_DWORD *)nLen = (unsigned __int8)bySelfRace;
      __trace(
        "CWeeklyGuildRankInfo::Send( %u, %d, %u, %u, %u ) : Find( byRace, dwGuildSerial ) -> %d!",
        v23,
        (unsigned int)dwClientIndex,
        (unsigned __int8)v25);
      if ( v19 < 0 )
      {
        v22->m_pkSendList[(unsigned __int8)v25].list[10].byRank = 0;
        memset_0(v22->m_pkSendList[(unsigned __int8)v25].list[10].wszGuildName, 0, 0x11ui64);
        v22->m_pkSendList[(unsigned __int8)v25].list[10].byGrade = 0;
        v22->m_pkSendList[(unsigned __int8)v25].list[10].dwPvpPoint = 0;
        v22->m_pkSendList[(unsigned __int8)v25].byExistSelfRankInfo = 0;
      }
      else
      {
        v22->m_pkSendList[(unsigned __int8)v25].list[10].byRank = v22->m_ppkRaceStartPos[(unsigned __int8)bySelfRace][v19]->m_wRank;
        strcpy_0(
          v22->m_pkSendList[(unsigned __int8)v25].list[10].wszGuildName,
          v22->m_ppkRaceStartPos[(unsigned __int8)bySelfRace][v19]->m_wszGuildName);
        v22->m_pkSendList[(unsigned __int8)v25].list[10].byGrade = v22->m_ppkRaceStartPos[(unsigned __int8)bySelfRace][v19]->m_byGrade;
        v22->m_pkSendList[(unsigned __int8)v25].list[10].dwPvpPoint = (signed int)floor(v22->m_ppkRaceStartPos[(unsigned __int8)v25][v19]->m_dKillPvpPoint + v22->m_ppkRaceStartPos[(unsigned __int8)bySelfRace][v19]->m_dGuildBattlePvpPoint);
        v22->m_pkSendList[(unsigned __int8)v25].byExistSelfRankInfo = 1;
      }
      v20 = 13;
      v21 = 40;
      v8 = _weekly_guild_rank_result_zocl::size(&v22->m_pkSendList[(unsigned __int8)v25]);
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &v20, (char *)&v22->m_pkSendList[(unsigned __int8)v25], v8);
      v11 = dwGuildSerial;
      *(_DWORD *)nLen = (unsigned __int8)bySelfRace;
      __trace(
        "CWeeklyGuildRankInfo::Send( %u, %d, %u, %u, %u ) : Send",
        v23,
        (unsigned int)dwClientIndex,
        (unsigned __int8)v25);
    }
    else
    {
      v11 = dwGuildSerial;
      *(_DWORD *)nLen = (unsigned __int8)bySelfRace;
      __trace(
        "CWeeklyGuildRankInfo::Send( %u, %d, %u, %u, %u ) : No Data!",
        dwVer,
        (unsigned int)n,
        (unsigned __int8)byTabRace);
      *(_DWORD *)szMsg = v22->m_pkSendList[(unsigned __int8)v25].dwVer;
      v16 = v25;
      pbyType = 13;
      v18 = 41;
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 5u);
    }
  }
}
