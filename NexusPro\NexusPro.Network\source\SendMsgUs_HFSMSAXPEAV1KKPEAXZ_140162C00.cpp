﻿/*
 * Function: ?SendMsg@Us_HFSM@@SAXPEAV1@KKPEAX@Z
 * Address: 0x140162C00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall Us_HFSM::SendMsg(Us_HFSM *pHFS, unsigned int dwFSMIndex, unsigned int dwMSG, void *lpParam)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  UsStateTBL *v6; // rax@12
  __int64 v7; // [sp+0h] [bp-38h]@1
  Us_FSM_Node *v8; // [sp+20h] [bp-18h]@10
  unsigned int dwFSMIndexa; // [sp+28h] [bp-10h]@14
  Us_HFSM *pHFSa; // [sp+40h] [bp+8h]@1
  unsigned int dwIndex; // [sp+48h] [bp+10h]@1
  unsigned int dwMSGa; // [sp+50h] [bp+18h]@1
  void *lpParama; // [sp+58h] [bp+20h]@1

  lpParama = lpParam;
  dwMSGa = dwMSG;
  dwIndex = dwFSMIndex;
  pHFSa = pHFS;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pHFSa )
  {
    if ( pHFSa->m_bSet )
    {
      if ( dwMSG )
      {
        if ( UsPoint<UsStateTBL>::operator UsStateTBL *(&pHFSa->m_spShareStateTBLPtr) )
        {
          if ( UsPoint<UsStateTBL>::operator->(&pHFSa->m_spShareStateTBLPtr)->m_pFun )
          {
            v8 = Us_HFSM::GetNode(pHFSa, dwIndex);
            if ( v8 )
            {
              if ( v8->m_bLive )
              {
                v6 = UsPoint<UsStateTBL>::operator->(&pHFSa->m_spShareStateTBLPtr);
                ((void (__fastcall *)(Us_HFSM *, _QWORD, _QWORD, void *))v6->m_pFun)(pHFSa, dwIndex, dwMSGa, lpParama);
              }
              if ( v8->m_pParent )
              {
                dwFSMIndexa = Us_HFSM::GetIndex(pHFSa, v8->m_pParent);
                if ( dwFSMIndexa != -1 )
                  Us_HFSM::SendMsg(pHFSa, dwFSMIndexa, dwMSGa, lpParama);
              }
            }
          }
        }
      }
    }
  }
}
