﻿/*
 * Function: ?SendList@CGuildList@@XGEE@Z
 * Address: 0x14025DB20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CGuildList::SendList(CGuildList *this, unsigned __int16 wIndex, char byRace, char byPage)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@8
  __int64 v7; // [sp+0h] [bp-118h]@1
  char pbyType; // [sp+34h] [bp-E4h]@8
  char v9; // [sp+35h] [bp-E3h]@8
  _guild_list_result_zocl Dst; // [sp+60h] [bp-B8h]@8
  unsigned __int64 v11; // [sp+100h] [bp-18h]@4
  CGuildList *v12; // [sp+120h] [bp+8h]@1
  unsigned __int16 v13; // [sp+128h] [bp+10h]@1
  char v14; // [sp+130h] [bp+18h]@1
  char v15; // [sp+138h] [bp+20h]@1

  v15 = byPage;
  v14 = byRace;
  v13 = wIndex;
  v12 = this;
  v4 = &v7;
  for ( i = 68LL; i; --i )
  {
    *(DWORD *)v4 = 0xCCCCCCCC;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byRace < 3 && (signed int)(unsigned __int8)byPage < 75 )
  {
    if ( (unsigned __int8)byPage > (signed int)v12->m_byMaxPage[(unsigned __int8)byRace] )
      v15 = v12->m_byMaxPage[(unsigned __int8)byRace];
    pbyType = 27;
    v9 = 116;
    _guild_list_result_zocl::_guild_list_result_zocl(&Dst);
    Dst.byMaxPage = v12->m_byMaxPage[(unsigned __int8)v14] + 1;
    Dst.byPage = v15;
    Dst.byListCnt = v12->m_pGuildList[(unsigned __int8)v14][(unsigned __int8)v15].byListCnt;
    memcpy_0(Dst.GuildList, v12->m_pGuildList[(unsigned __int8)v14][(unsigned __int8)v15].GuildList, 0x8Cui64);
    v6 = _guild_list_result_zocl::size(&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2088, v13, &pbyType, &Dst.byPage, v6);
  }
}

