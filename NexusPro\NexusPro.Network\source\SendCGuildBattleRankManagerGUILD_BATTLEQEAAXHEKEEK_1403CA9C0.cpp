﻿/*
 * Function: ?Send@CGuildBattleRankManager@GUILD_BATTLE@@QEAAXHEKEEK@Z
 * Address: 0x1403CA9C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall GUILD_BATTLE::CGuildBattleRankManager::Send(GUILD_BATTLE::CGuildBattleRankManager *this, int n, char bySelfRace, unsigned int dwCurVer, char byTabRace, char byPage, unsigned int dwGuildSerial)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v9; // ax@16
  __int64 v10; // [sp+0h] [bp-D8h]@1
  char szMsg; // [sp+34h] [bp-A4h]@10
  unsigned int v12; // [sp+35h] [bp-A3h]@10
  char pbyType; // [sp+54h] [bp-84h]@10
  char v14; // [sp+55h] [bp-83h]@10
  int iFindInx; // [sp+74h] [bp-64h]@13
  char byFindPage; // [sp+94h] [bp-44h]@13
  char v17; // [sp+B4h] [bp-24h]@16
  char v18; // [sp+B5h] [bp-23h]@16
  GUILD_BATTLE::CGuildBattleRankManager *v19; // [sp+E0h] [bp+8h]@1
  int dwClientIndex; // [sp+E8h] [bp+10h]@1
  char v21; // [sp+F0h] [bp+18h]@1

  v21 = bySelfRace;
  dwClientIndex = n;
  v19 = this;
  v7 = &v10;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( (signed int)(unsigned __int8)bySelfRace < 3
    && (signed int)(unsigned __int8)byTabRace < 3
    && (unsigned __int8)byPage < 0x1Eu
    && v19->m_dwVer[(unsigned __int8)byTabRace] != dwCurVer )
  {
    if ( v19->m_ppkList[(unsigned __int8)byTabRace]->byMaxPage )
    {
      if ( v19->m_ppkList[(unsigned __int8)byTabRace]->byMaxPage > (signed int)(unsigned __int8)byPage && v19->m_ppkList )
      {
        iFindInx = -1;
        byFindPage = -1;
        if ( GUILD_BATTLE::CGuildBattleRankManager::Find(v19, bySelfRace, dwGuildSerial, &iFindInx, &byFindPage) )
        {
          v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].byExistSelfGuildInfo = 1;
          v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].list[10].nRank = v19->m_ppkList[(unsigned __int8)v21][(unsigned __int8)byFindPage].list[iFindInx].nRank;
          v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].list[10].byGrade = v19->m_ppkList[(unsigned __int8)v21][(unsigned __int8)byFindPage].list[iFindInx].byGrade;
          strcpy_0(
            v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].list[10].wszName,
            v19->m_ppkList[(unsigned __int8)v21][(unsigned __int8)byFindPage].list[iFindInx].wszName);
          v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].list[10].dwWin = v19->m_ppkList[(unsigned __int8)v21][(unsigned __int8)byFindPage].list[iFindInx].dwWin;
          v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].list[10].dwLose = v19->m_ppkList[(unsigned __int8)v21][(unsigned __int8)byFindPage].list[iFindInx].dwLose;
          v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].list[10].dwDraw = v19->m_ppkList[(unsigned __int8)v21][(unsigned __int8)byFindPage].list[iFindInx].dwDraw;
          v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].list[10].dwScore = v19->m_ppkList[(unsigned __int8)v21][(unsigned __int8)byFindPage].list[iFindInx].dwScore;
        }
        else
        {
          v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage].byExistSelfGuildInfo = 0;
        }
        v17 = 27;
        v18 = 52;
        v9 = _guild_battle_rank_list_result_zocl::size(&v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage]);
        CNetProcess::LoadSendMsg(
          unk_1414F2088,
          dwClientIndex,
          &v17,
          (char *)&v19->m_ppkList[(unsigned __int8)byTabRace][(unsigned __int8)byPage],
          v9);
      }
    }
    else
    {
      szMsg = byTabRace;
      v12 = v19->m_dwVer[(unsigned __int8)byTabRace];
      pbyType = 27;
      v14 = 90;
      CNetProcess::LoadSendMsg(unk_1414F2088, n, &pbyType, &szMsg, 5u);
    }
  }
}
