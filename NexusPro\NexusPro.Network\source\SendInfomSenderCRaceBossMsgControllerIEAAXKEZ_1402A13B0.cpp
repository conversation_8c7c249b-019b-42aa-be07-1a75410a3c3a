﻿/*
 * Function: ?SendInfomSender@CRaceBossMsgController@@IEAAXKE@Z
 * Address: 0x1402A13B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CRaceBossMsgController::SendInfomSender(CRaceBossMsgController *this, unsigned int dwSerial, char ucRemainCnt)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  CPlayer *v6; // [sp+30h] [bp-58h]@4
  char szMsg; // [sp+44h] [bp-44h]@6
  char pbyType; // [sp+64h] [bp-24h]@6
  char v9; // [sp+65h] [bp-23h]@6
  char v10; // [sp+A0h] [bp+18h]@1

  v10 = ucRemainCnt;
  v3 = &v5;
  for ( i = 32LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = GetPtrPlayerFromSerial(&g_Player, 2532, dwSerial);
  if ( v6 )
  {
    if ( v6->m_bLive )
    {
      szMsg = v10;
      pbyType = 52;
      v9 = 4;
      CNetProcess::LoadSendMsg(unk_1414F2088, v6->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
    }
  }
}

