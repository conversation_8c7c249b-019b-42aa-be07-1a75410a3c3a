﻿/*
 * Function: ?SendMsg_ChannelClose@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x14026C4A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CDarkHoleChannel::SendMsg_ChannelClose(CDarkHoleChannel *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@7
  __int64 v4; // [sp+0h] [bp-78h]@1
  _darkhole_channel_close_inform_zocl v5; // [sp+34h] [bp-44h]@7
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  _dh_player_mgr *v9; // [sp+68h] [bp-10h]@6
  CDarkHoleChannel *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pbyType = 35;
  v7 = 12;
  for ( j = 0; j < 32; ++j )
  {
    v9 = &v10->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v9) )
    {
      v3 = _darkhole_channel_close_inform_zocl::size(&v5);
      CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_Quester[j].pOne->m_ObjID.m_wIndex, &pbyType, &v5.sDum, v3);
    }
  }
}
