﻿/*
 * Function: ?SendCurrentBattleInfoRequest@CGuildBattleController@@QEAAXHI@Z
 * Address: 0x1403D6A80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CGuildBattleController::SendCurrentBattleInfoRequest(CGuildBattleController *this, int n, unsigned int uiMapID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  int na; // [sp+38h] [bp+10h]@1
  unsigned int uiMapIDa; // [sp+40h] [bp+18h]@1

  uiMapIDa = uiMapID;
  na = n;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
  GUILD_BATTLE::CCurrentGuildBattleInfoManager::Send(v5, na, uiMapIDa);
}
