﻿/*
 * Function: ?SendFirst@CPossibleBattleGuildListManager@GUILD_BATTLE@@XHE@Z
 * Address: 0x1403D9920
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  GUILD_BATTLE::CPossibleBattleGuildListManager::SendFirst(GUILD_BATTLE::CPossibleBattleGuildListManager *this, int n, char byRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CPossibleBattleGuildListManager *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 12LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  GUILD_BATTLE::CPossibleBattleGuildListManager::SendInfo(v6, n, byRace, 0, 0);
}

