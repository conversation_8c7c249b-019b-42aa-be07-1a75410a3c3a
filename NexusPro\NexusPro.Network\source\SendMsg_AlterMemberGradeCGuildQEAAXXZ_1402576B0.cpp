﻿/*
 * Function: ?SendMsg_AlterMemberGrade@CGuild@@XXZ
 * Address: 0x1402576B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CGuild::SendMsg_AlterMemberGrade(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@14
  __int64 v4; // [sp+0h] [bp-1B8h]@1
  _guild_alter_member_grade_inform_zocl v5; // [sp+40h] [bp-178h]@4
  int v6; // [sp+174h] [bp-44h]@4
  int j; // [sp+178h] [bp-40h]@4
  _guild_member_info *v8; // [sp+180h] [bp-38h]@7
  char pbyType; // [sp+194h] [bp-24h]@9
  char v10; // [sp+195h] [bp-23h]@9
  CGuild *v11; // [sp+1C0h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 108LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _guild_alter_member_grade_inform_zocl::_guild_alter_member_grade_inform_zocl(&v5);
  v6 = 0;
  for ( j = 0; j < 50; ++j )
  {
    v8 = &v11->m_MemberData[j];
    if ( _guild_member_info::IsFill(v8) )
    {
      v5.MemberList[v6].dwMemberSerial = v8->dwSerial;
      v5.MemberList[v6].byRank = v8->byRank;
      v5.MemberList[v6++].byGrade = v8->byClassInGuild;
    }
  }
  v5.byAlterMemberNum = v6;
  pbyType = 27;
  v10 = 32;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v11->m_MemberData[j]) && v11->m_MemberData[j].pPlayer )
    {
      v3 = _guild_alter_member_grade_inform_zocl::size(&v5);
      CNetProcess::LoadSendMsg(
        unk_1414F2088,
        v11->m_MemberData[j].pPlayer->m_ObjID.m_wIndex,
        &pbyType,
        &v5.byAlterMemberNum,
        v3);
    }
  }
}

