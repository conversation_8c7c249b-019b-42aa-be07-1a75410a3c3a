﻿/*
 * Function: ?SendChangeAggroData@CMonsterAggroMgr@@XXZ
 * Address: 0x14015E950
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CMonsterAggroMgr::SendChangeAggroData(CMonsterAggroMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  CPlayer *v5; // [sp+28h] [bp-20h]@8
  CPlayer *v6; // [sp+30h] [bp-18h]@10
  CGameObject *v7; // [sp+38h] [bp-10h]@11
  CMonsterAggroMgr *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 10; ++j )
  {
    if ( CAggroNode::IsLive(&v8->m_AggroPool[j]) )
    {
      v5 = (CPlayer *)v8->m_AggroPool[j].m_pCharacter;
      if ( !v5->m_ObjID.m_byKind && !v5->m_ObjID.m_byID )
      {
        v6 = v5;
        if ( v5->m_byUserDgr >= 3 )
        {
          v7 = CPlayer::GetTargetObj(v6);
          if ( v7 )
          {
            if ( v7 == (CGameObject *)v8->m_pMonster )
              CPlayer::SendMsg_MonsterAggroData(v6, (CCharacter *)&v8->m_pMonster->vfptr);
          }
        }
      }
    }
  }
}

