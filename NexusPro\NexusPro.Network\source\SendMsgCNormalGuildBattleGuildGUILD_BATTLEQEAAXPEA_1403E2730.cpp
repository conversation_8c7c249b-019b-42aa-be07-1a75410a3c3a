﻿/*
 * Function: ?SendMsg@CNormalGuildBattleGuild@GUILD_BATTLE@@XPEAEPEADI@Z
 * Address: 0x1403E2730
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(GUILD_BATTLE::CNormalGuildBattleGuild *this, char *byType, char *pMsg, unsigned int uiSize)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@8
  __int64 v7; // [sp+0h] [bp-48h]@1
  int j; // [sp+30h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v9; // [sp+50h] [bp+8h]@1
  char *pbyType; // [sp+58h] [bp+10h]@1
  char *szMsg; // [sp+60h] [bp+18h]@1
  unsigned int v12; // [sp+68h] [bp+20h]@1

  v12 = uiSize;
  szMsg = pMsg;
  pbyType = byType;
  v9 = this;
  v4 = &v7;
  for ( i = 16LL; i; --i )
  {
    *(DWORD *)v4 = 0xCCCCCCCC;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  for ( j = 0; j < 50; ++j )
  {
    if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v9->m_kMember[j]) )
    {
      v6 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex(&v9->m_kMember[j]);
      CNetProcess::LoadSendMsg(unk_1414F2088, v6, pbyType, szMsg, v12);
    }
  }
}

