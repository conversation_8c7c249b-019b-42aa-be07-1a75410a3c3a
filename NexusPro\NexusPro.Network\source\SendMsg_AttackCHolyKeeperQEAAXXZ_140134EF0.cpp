﻿/*
 * Function: ?SendMsg_Attack@<PERSON>oly<PERSON>eeper@@XXZ
 * Address: 0x140134EF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CHolyKeeper::SendMsg_Attack(CHolyKeeper *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@7
  __int64 v4; // [sp+0h] [bp-1C8h]@1
  _attack_keeper_inform_zocl v5; // [sp+40h] [bp-188h]@4
  int j; // [sp+194h] [bp-34h]@4
  char pbyType; // [sp+1A4h] [bp-24h]@7
  char v8; // [sp+1A5h] [bp-23h]@7
  CHolyKeeper *v9; // [sp+1D0h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 112LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _attack_keeper_inform_zocl::_attack_keeper_inform_zocl(&v5);
  v5.dwAtterSerial = v9->m_dwObjSerial;
  v5.bCritical = v9->m_at->m_bIsCrtAtt;
  v5.byListNum = v9->m_at->m_nDamagedObjNum;
  for ( j = 0; j < v9->m_at->m_nDamagedObjNum; ++j )
  {
    v5.DamList[j].byDstID = v9->m_at->m_DamList[j].m_pChar->m_ObjID.m_byID;
    v5.DamList[j].dwDstSerial = v9->m_at->m_DamList[j].m_pChar->m_dwObjSerial;
    v5.DamList[j].wDamage = v9->m_at->m_DamList[j].m_nDamage;
  }
  pbyType = 5;
  v8 = -105;
  v3 = _attack_keeper_inform_zocl::size(&v5);
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, (char *)&v5, v3, 0);
}

