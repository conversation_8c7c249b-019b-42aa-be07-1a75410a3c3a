﻿/*
 * Function: ?SendMsg@CNormalGuildBattleGuild@GUILD_BATTLE@@XPEAEPEADIK@Z
 * Address: 0x1403E28E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(GUILD_BATTLE::CNormalGuildBattleGuild *this, char *byType, char *pMsg, unsigned int uiSize, unsigned int dwSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v7; // ax@10
  __int64 v8; // [sp+0h] [bp-48h]@1
  int j; // [sp+30h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v10; // [sp+50h] [bp+8h]@1
  char *pbyType; // [sp+58h] [bp+10h]@1
  char *szMsg; // [sp+60h] [bp+18h]@1
  unsigned int v13; // [sp+68h] [bp+20h]@1

  v13 = uiSize;
  szMsg = pMsg;
  pbyType = byType;
  v10 = this;
  v5 = &v8;
  for ( i = 16LL; i; --i )
  {
    *(DWORD *)v5 = 0xCCCCCCCC;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  for ( j = 0; j < 50; ++j )
  {
    if ( dwSerial != GUILD_BATTLE::CNormalGuildBattleGuildMember::GetSerial(&v10->m_kMember[j])
      && GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v10->m_kMember[j]) )
    {
      v7 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex(&v10->m_kMember[j]);
      CNetProcess::LoadSendMsg(unk_1414F2088, v7, pbyType, szMsg, v13);
    }
  }
}

