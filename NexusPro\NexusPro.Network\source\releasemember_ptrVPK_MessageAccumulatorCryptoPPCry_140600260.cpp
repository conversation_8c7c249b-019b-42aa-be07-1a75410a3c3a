﻿/*
 * Function: ?release@?$member_ptr@VPK_MessageAccumulator@CryptoPP@@@CryptoPP@@PEAVPK_MessageAccumulator@2@XZ
 * Address: 0x140600260
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

__int64  CryptoPP::member_ptr<CryptoPP::PK_MessageAccumulator>::release(__int64 *a1)
{
  __int64 v1; // ST00_8@1

  v1 = *a1;
  *a1 = 0LL;
  return v1;
}

