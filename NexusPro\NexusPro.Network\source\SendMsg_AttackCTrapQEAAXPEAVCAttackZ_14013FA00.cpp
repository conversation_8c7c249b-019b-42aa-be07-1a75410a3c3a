﻿/*
 * Function: ?SendMsg_Attack@CTrap@@XPEAVCAttack@@@Z
 * Address: 0x14013FA00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CTrap::SendMsg_Attack(CTrap *this, CAttack *pAt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-1C8h]@1
  _attack_trap_inform_zocl v6; // [sp+40h] [bp-188h]@4
  int j; // [sp+194h] [bp-34h]@4
  char pbyType; // [sp+1A4h] [bp-24h]@7
  char v9; // [sp+1A5h] [bp-23h]@7
  CTrap *v10; // [sp+1D0h] [bp+8h]@1
  CAttack *v11; // [sp+1D8h] [bp+10h]@1

  v11 = pAt;
  v10 = this;
  v2 = &v5;
  for ( i = 112LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _attack_trap_inform_zocl::_attack_trap_inform_zocl(&v6);
  v6.dwAtterSerial = v10->m_dwObjSerial;
  v6.bCritical = v11->m_bIsCrtAtt;
  v6.byListNum = v11->m_nDamagedObjNum;
  for ( j = 0; j < v11->m_nDamagedObjNum; ++j )
  {
    v6.DamList[j].byDstID = v11->m_DamList[j].m_pChar->m_ObjID.m_byID;
    v6.DamList[j].dwDstSerial = v11->m_DamList[j].m_pChar->m_dwObjSerial;
    v6.DamList[j].wDamage = v11->m_DamList[j].m_nDamage;
  }
  pbyType = 5;
  v9 = -104;
  v4 = _attack_trap_inform_zocl::size(&v6);
  CGameObject::CircleReport((CGameObject *)&v10->vfptr, &pbyType, (char *)&v6, v4, 0);
}

