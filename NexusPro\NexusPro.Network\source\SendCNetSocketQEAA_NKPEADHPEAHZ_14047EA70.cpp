﻿/*
 * Function: ?Send@CNetSocket@@QEAA_NKPEADHPEAH@Z
 * Address: 0x14047EA70
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

char __fastcall CNetSocket::Send(CNetSocket *this, unsigned int n, char *pBuf, int nSize, int *pnRet)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v8; // [sp+0h] [bp-28h]@1
  CNetSocket *v9; // [sp+30h] [bp+8h]@1
  unsigned int v10; // [sp+38h] [bp+10h]@1

  v10 = n;
  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  *pnRet = send(v9->m_Socket[n].m_Socket, pBuf, nSize, 0);
  if ( *pnRet == -1 )
  {
    *pnRet = WSAGetLastError();
    if ( *pnRet == 10035 )
    {
      ++v9->m_TotalCount.m_dwTotalSendBlockNum;
      ++v9->m_Socket[v10].m_dwTotalRecvBlock;
    }
    else
    {
      ++v9->m_TotalCount.m_dwTotalSendErrNum;
    }
    result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
