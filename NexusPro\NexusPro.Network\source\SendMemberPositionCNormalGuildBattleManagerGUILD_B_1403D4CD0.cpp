﻿/*
 * Function: ?SendMemberPosition@CNormalGuildBattleManager@GUILD_BATTLE@@XKK@Z
 * Address: 0x1403D4CD0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  GUILD_BATTLE::CNormalGuildBattleManager::SendMemberPosition(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@6
  __int64 v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@6
  GUILD_BATTLE::CNormalGuildBattle *v8; // [sp+30h] [bp-18h]@5
  GUILD_BATTLE::CNormalGuildBattleManager *v9; // [sp+50h] [bp+8h]@1
  int dwGuildSeriala; // [sp+58h] [bp+10h]@1
  unsigned int dwChracSerial; // [sp+60h] [bp+18h]@1

  dwChracSerial = dwCharacSerial;
  dwGuildSeriala = dwGuildSerial;
  v9 = this;
  v3 = &v6;
  for ( i = 16LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( dwGuildSerial != -1 )
  {
    v8 = 0LL;
    v8 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v9, dwGuildSerial);
    if ( v8 )
    {
      if ( GUILD_BATTLE::CNormalGuildBattle::IsInBattle(v8) )
        GUILD_BATTLE::CNormalGuildBattle::NotifyCommitteeMemberPosition(v8, dwGuildSeriala, dwChracSerial);
    }
    else
    {
      v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v7 = dwGuildSeriala;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v5,
        "CNormalGuildBattleManager::SendMemberPosition( dwGuildSerial(%u), dwChracSerial(%u) ) : dwGuildSerial(%u) Invalid!",
        (unsigned int)dwGuildSeriala,
        dwChracSerial);
    }
  }
}

