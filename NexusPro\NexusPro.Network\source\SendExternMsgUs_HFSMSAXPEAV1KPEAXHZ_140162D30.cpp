﻿/*
 * Function: ?SendExternMsg@Us_HFSM@@SAXPEAV1@KPEAXH@Z
 * Address: 0x140162D30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  Us_HFSM::SendExternMsg(Us_HFSM *pHFS, unsigned int dwMSG, void *lpParam, int nParam)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  UsStateTBL *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-28h]@1
  Us_HFSM *v8; // [sp+30h] [bp+8h]@1
  unsigned int v9; // [sp+38h] [bp+10h]@1
  void *v10; // [sp+40h] [bp+18h]@1
  int v11; // [sp+48h] [bp+20h]@1

  v11 = nParam;
  v10 = lpParam;
  v9 = dwMSG;
  v8 = pHFS;
  v4 = &v7;
  for ( i = 8LL; i; --i )
  {
    *(DWORD *)v4 = 0xCCCCCCCC;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v8 && v8->m_bSet && UsPoint<UsStateTBL>::operator UsStateTBL *(&v8->m_spShareStateTBLPtr) )
  {
    if ( UsPoint<UsStateTBL>::operator->(&v8->m_spShareStateTBLPtr)->m_pExternFun )
    {
      v6 = UsPoint<UsStateTBL>::operator->(&v8->m_spShareStateTBLPtr);
      ((void ( *)(Us_HFSM *, _QWORD, void *, _QWORD))v6->m_pExternFun)(v8, v9, v10, (unsigned int)v11);
    }
  }
}

