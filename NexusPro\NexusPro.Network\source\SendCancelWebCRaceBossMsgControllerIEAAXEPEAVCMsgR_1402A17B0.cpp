﻿/*
 * Function: ?SendCancelWeb@CRaceBossMsgController@@IEAAXEPEAVCMsg@RACE_BOSS_MSG@@@Z
 * Address: 0x1402A17B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CRaceBossMsgController::SendCancelWeb(CRaceBossMsgController *this, char ucRace, RACE_BOSS_MSG::CMsg *pkMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  unsigned int Dst; // [sp+38h] [bp-50h]@7
  int v7; // [sp+3Ch] [bp-4Ch]@7
  char v8; // [sp+40h] [bp-48h]@7
  char pbyType; // [sp+64h] [bp-24h]@7
  char v10; // [sp+65h] [bp-23h]@7
  char v11; // [sp+98h] [bp+10h]@1
  RACE_BOSS_MSG::CMsg *v12; // [sp+A0h] [bp+18h]@1

  v12 = pkMsg;
  v11 = ucRace;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( unk_1799C608D && unk_1799C608E )
  {
    memset_0(&Dst, 0, 9ui64);
    Dst = RACE_BOSS_MSG::CMsg::GetID(v12);
    v7 = unk_1799C608C;
    v8 = v11;
    pbyType = 51;
    v10 = 11;
    if ( unk_1799C9ADE )
      CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, (char *)&Dst, 9u);
  }
}
