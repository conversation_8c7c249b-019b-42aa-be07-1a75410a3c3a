﻿/*
 * Function: ?SendMsgRequestResult@CRaceBossMsgController@@IEAAXGE@Z
 * Address: 0x1402A1060
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CRaceBossMsgController::SendMsgRequestResult(CRaceBossMsgController *this, unsigned __int16 usInx, char ucRet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4

  v3 = &v5;
  for ( i = 28LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = ucRet;
  pbyType = 52;
  v8 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, usInx, &pbyType, &szMsg, 1u);
}

