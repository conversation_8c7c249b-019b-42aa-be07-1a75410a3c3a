﻿/*
 * Function: ?SendMsg_Destroy@CDarkHole@@QEAAXXZ
 * Address: 0x140164040
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CDarkHole::SendMsg_Destroy(CDarkHole *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@14
  int v4; // eax@17
  int v5; // eax@17
  __int64 v6; // [sp+0h] [bp-88h]@1
  _darkhole_destroy_zocl v7; // [sp+34h] [bp-54h]@5
  char pbyType; // [sp+54h] [bp-34h]@5
  char v9; // [sp+55h] [bp-33h]@5
  CPartyPlayer **v10; // [sp+68h] [bp-20h]@8
  int j; // [sp+70h] [bp-18h]@9
  CDarkHole *v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v1 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v12->m_pChannel )
  {
    v7.wIndex = v12->m_ObjID.m_wIndex;
    v7.dwSerial = v12->m_dwObjSerial;
    pbyType = 35;
    v9 = 102;
    if ( v12->m_pChannel->m_pQuestSetup )
    {
      if ( v12->m_pChannel->m_pQuestSetup->bPartyOnly )
      {
        if ( CPartyPlayer::IsPartyMode(v12->m_pChannel->m_pPartyMng) )
        {
          v10 = CPartyPlayer::GetPtrPartyMember(v12->m_pChannel->m_pPartyMng);
          if ( v10 )
          {
            for ( j = 0; j < 8; ++j )
            {
              if ( v10[j] )
              {
                if ( *(&g_Player.m_bLive + 50856 * v10[j]->m_id.wIndex) )
                {
                  v3 = _darkhole_destroy_zocl::size(&v7);
                  CNetProcess::LoadSendMsg(unk_1414F2088, v10[j]->m_id.wIndex, &pbyType, (char *)&v7, v3);
                }
              }
            }
          }
        }
      }
      else
      {
        v4 = _darkhole_destroy_zocl::size(&v7);
        CDarkHoleChannel::SendMsg_GateDestroy(v12->m_pChannel, &pbyType, (char *)&v7, v4);
        v5 = _darkhole_destroy_zocl::size(&v7);
        CGameObject::CircleReport((CGameObject *)&v12->vfptr, &pbyType, (char *)&v7, v5, 0);
      }
    }
  }
}
