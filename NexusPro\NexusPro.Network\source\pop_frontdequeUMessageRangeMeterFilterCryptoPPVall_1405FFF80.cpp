﻿/*
 * Function: ?pop_front@?$deque@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@@std@@QEAAXXZ
 * Address: 0x1405FFF80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

__int64 __fastcall std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::pop_front(__int64 a1)
{
  __int64 result; // rax@1
  __int64 v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  result = std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::empty(a1);
  if ( !(_BYTE)result )
  {
    std::allocator<CryptoPP::MeterFilter::MessageRange>::destroy(
      v2 + 16,
      *(_QWORD *)(*(_QWORD *)(v2 + 24) + 8i64 * *(_QWORD *)(v2 + 40)));
    if ( *(_QWORD *)(v2 + 32) <= ++*(_QWORD *)(v2 + 40) )
      *(_QWORD *)(v2 + 40) = 0i64;
    --*(_QWORD *)(v2 + 48);
    result = v2;
    if ( !*(_QWORD *)(v2 + 48) )
    {
      result = v2;
      *(_QWORD *)(v2 + 40) = 0i64;
    }
  }
  return result;
}
