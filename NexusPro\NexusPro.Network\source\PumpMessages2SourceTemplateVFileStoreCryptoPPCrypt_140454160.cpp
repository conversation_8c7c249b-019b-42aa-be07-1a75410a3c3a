﻿/*
 * Function: ?PumpMessages2@?$SourceTemplate@VFileStore@CryptoPP@@@CryptoPP@@UEAA_KAEAI_N@Z
 * Address: 0x140454160
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

__int64 __fastcall CryptoPP::SourceTemplate<CryptoPP::FileStore>::PumpMessages2(CryptoPP::SourceTemplate<CryptoPP::FileStore> *this, unsigned int *messageCount, bool blocking)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  bool v7; // [sp+20h] [bp-18h]@4
  CryptoPP::SourceTemplate<CryptoPP::FileStore> *v8; // [sp+40h] [bp+8h]@1
  bool v9; // [sp+50h] [bp+18h]@1

  v9 = blocking;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  ((void (__fastcall *)(CryptoPP::SourceTemplate<CryptoPP::FileStore> *))v8->vfptr[20].Clone)(v8);
  v7 = v9;
  return CryptoPP::BufferedTransformation::TransferMessagesTo2((unsigned __int8)v8 + 48);
}
