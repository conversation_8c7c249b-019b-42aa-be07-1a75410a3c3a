﻿/*
 * Function: ?SendMsg_ChangeTaxRate@CGuild@@QEAAXE@Z
 * Address: 0x140255730
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CGuild::SendMsg_ChangeTaxRate(CGuild *this, char byTax)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char v6; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  CGuild *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = v10->m_dwSerial;
  v6 = byTax;
  pbyType = 27;
  v8 = 100;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v10->m_MemberData[j]) )
    {
      if ( v10->m_MemberData[j].pPlayer )
        CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_MemberData[j].pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 5u);
    }
  }
}
