﻿/*
 * Function: ?_Ufill@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@IEAAPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEAU34@_KAEBU34@@Z
 * Address: 0x1405932E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Ufill(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v5; // [sp+38h] [bp+10h]@1
  __int64 v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  stdext::unchecked_uninitialized_fill_n<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,unsigned __int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(
    a2,
    a3,
    a4,
    a1 + 8);
  return 80 * v6 + v5;
}

