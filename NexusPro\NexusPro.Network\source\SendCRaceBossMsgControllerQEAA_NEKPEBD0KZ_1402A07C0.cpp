﻿/*
 * Function: ?Send@CRaceBossMsgController@@QEAA_NEKPEBD0K@Z
 * Address: 0x1402A07C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

char __fastcall CRaceBossMsgController::Send(CRaceBossMsgController *this, char ucRace, unsigned int dwSerial, const char *wszName, const char *pwszMsg, unsigned int dwWebSendDBID)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPvpUserAndGuildRankingSystem *v9; // rax@8
  __int64 v10; // [sp+0h] [bp-68h]@1
  RACE_BOSS_MSG::CMsg *pkMsg; // [sp+48h] [bp-20h]@10
  int iRet; // [sp+54h] [bp-14h]@10
  CRaceBossMsgController *v13; // [sp+70h] [bp+8h]@1
  char v14; // [sp+78h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+80h] [bp+18h]@1
  char *pwszName; // [sp+88h] [bp+20h]@1

  pwszName = (char *)wszName;
  dwSeriala = dwSerial;
  v14 = ucRace;
  v13 = this;
  v6 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( unk_1799C9ADE )
  {
    if ( dwSerial )
    {
      v9 = CPvpUserAndGuildRankingSystem::Instance();
      if ( CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v9, v14, 0) == dwSeriala )
      {
        pkMsg = 0i64;
        iRet = RACE_BOSS_MSG::CMsgListManager::Send(
                 &v13->m_kManager,
                 v14,
                 dwSeriala,
                 pwszName,
                 pwszMsg,
                 &pkMsg,
                 dwWebSendDBID);
        if ( iRet )
        {
          CRaceBossMsgController::SendWebRaceBossSMSErrorResult(v13, iRet, dwWebSendDBID);
          result = 0;
        }
        else
        {
          CRaceBossMsgController::SendComfirmWeb(v13, v14, pkMsg);
          CRaceBossMsgController::SendConfirmCtrl(v13, v14, pkMsg);
          result = 1;
        }
      }
      else
      {
        CRaceBossMsgController::SendWebRaceBossSMSErrorResult(v13, 3, dwWebSendDBID);
        result = 0;
      }
    }
    else
    {
      CRaceBossMsgController::SendWebRaceBossSMSErrorResult(v13, 2, dwWebSendDBID);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
