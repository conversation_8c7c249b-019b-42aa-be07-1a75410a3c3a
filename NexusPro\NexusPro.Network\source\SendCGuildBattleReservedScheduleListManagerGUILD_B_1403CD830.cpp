﻿/*
 * Function: ?Send@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAAXIHKEEK@Z
 * Address: 0x1403CD830
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Send(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int uiMapID, int n, unsigned int dwVer, char byDay, char byPage, unsigned int dwGuildSerial)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-48h]@1
  unsigned int dwVera; // [sp+20h] [bp-28h]@6
  char v11; // [sp+28h] [bp-20h]@6
  unsigned int v12; // [sp+30h] [bp-18h]@6
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v7 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( (signed int)(unsigned __int8)byDay < 2 )
  {
    if ( byDay )
    {
      v12 = dwGuildSerial;
      v11 = byPage;
      dwVera = dwVer;
      GUILD_BATTLE::CReservedGuildScheduleDayGroup::Send(v13->m_pkTomorrow, 1, uiMapID, n, dwVer, byPage, dwGuildSerial);
    }
    else
    {
      v12 = dwGuildSerial;
      v11 = byPage;
      dwVera = dwVer;
      GUILD_BATTLE::CReservedGuildScheduleDayGroup::Send(v13->m_pkToday, 0, uiMapID, n, dwVer, byPage, dwGuildSerial);
    }
  }
}
