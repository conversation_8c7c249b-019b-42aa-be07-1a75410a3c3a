﻿/*
 * Function: ?SendDrawResult@CNormalGuildBattle@GUILD_BATTLE@@IEAAXXZ
 * Address: 0x1403E78E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  GUILD_BATTLE::CNormalGuildBattle::SendDrawResult(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@4
  char *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-98h]@1
  char Dest; // [sp+28h] [bp-70h]@4
  char v7; // [sp+39h] [bp-5Fh]@4
  char byType; // [sp+64h] [bp-34h]@4
  char v9; // [sp+65h] [bp-33h]@4
  unsigned __int64 v10; // [sp+80h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v11; // [sp+A0h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for ( i = 36LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  byType = 27;
  v9 = 76;
  v3 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v11->m_k1P);
  strcpy_0(&Dest, v3);
  v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v11->m_k2P);
  strcpy_0(&v7, v4);
  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v11->m_k1P, &byType, &Dest, 0x22u);
  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v11->m_k2P, &byType, &Dest, 0x22u);
}

