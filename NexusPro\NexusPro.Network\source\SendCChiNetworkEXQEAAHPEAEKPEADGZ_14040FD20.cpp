﻿/*
 * Function: ?Send@CChiNetworkEX@@QEAAHPEAEKPEADG@Z
 * Address: 0x14040FD20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

int __fastcall CChiNetworkEX::Send(CChiNetworkEX *this, char *pbyType, unsigned int dwSID, char *szMsg, unsigned __int16 nLen)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  char *v9; // [sp+20h] [bp-18h]@4
  unsigned __int16 v10; // [sp+28h] [bp-10h]@4
  CChiNetworkEX *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v10 = nLen;
  v9 = szMsg;
  return ((int (__fastcall *)(_QWORD, _QWORD, char *, _QWORD))v11->LoadSendMsg)(0i64, 0i64, pbyType, dwSID);
}
