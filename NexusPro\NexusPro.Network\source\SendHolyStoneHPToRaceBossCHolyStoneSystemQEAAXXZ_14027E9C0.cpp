﻿/*
 * Function: ?SendHolyStoneHPToRaceBoss@CHolyStoneSystem@@XXZ
 * Address: 0x14027E9C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CHolyStoneSystem::SendHolyStoneHPToRaceBoss(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int16 v3; // ax@6
  CPvpUserAndGuildRankingSystem *v4; // rax@11
  __int64 v5; // [sp+0h] [bp-98h]@1
  __int16 Dst[8]; // [sp+34h] [bp-64h]@4
  int j; // [sp+44h] [bp-54h]@4
  char pbyType; // [sp+54h] [bp-44h]@7
  char v9; // [sp+55h] [bp-43h]@7
  int k; // [sp+64h] [bp-34h]@7
  CPlayer *v11; // [sp+68h] [bp-30h]@10
  unsigned int dwSerial; // [sp+78h] [bp-20h]@11
  int v13; // [sp+7Ch] [bp-1Ch]@11
  unsigned __int64 v14; // [sp+80h] [bp-18h]@4

  v1 = &v5;
  for ( i = 36LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  memset_0(Dst, 0, 6ui64);
  for ( j = 0; j < 3; ++j )
  {
    v3 = (*(int ( **)(struct CHolyStone *))&g_Stone[j].vfptr->gap8[8])(&g_Stone[j]);
    Dst[j] = v3;
  }
  pbyType = 13;
  v9 = 33;
  for ( k = 0; k < 2532; ++k )
  {
    v11 = &g_Player + k;
    if ( v11->m_bLive )
    {
      dwSerial = CPlayerDB::GetCharSerial(&v11->m_Param);
      v13 = CPlayerDB::GetRaceCode(&v11->m_Param);
      v4 = CPvpUserAndGuildRankingSystem::Instance();
      if ( CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v4, v13, dwSerial) )
        CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, (char *)Dst, 6u);
    }
  }
}

