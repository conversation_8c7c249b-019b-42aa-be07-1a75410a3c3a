﻿/*
 * Function: ?SendDQS_RoomUpdate@CGuildRoomInfo@@XXZ
 * Address: 0x1402E6790
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CGuildRoomInfo::SendDQS_RoomUpdate(CGuildRoomInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  unsigned int Dst; // [sp+34h] [bp-24h]@4
  CGuildRoomInfo *v5; // [sp+60h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 20LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(&Dst, 0, 4ui64);
  Dst = v5->m_dwGuildSerial;
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0LL, 72, (char *)&Dst, 4);
}

