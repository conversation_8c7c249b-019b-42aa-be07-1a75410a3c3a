﻿/*
 * Function: ?Ref@?$Singleton@VDL_SignatureMessageEncodingMethod_DSA@CryptoPP@@U?$NewObject@VDL_SignatureMessageEncodingMethod_DSA@CryptoPP@@@2@$0A@@CryptoPP@@QEBAAEBVDL_SignatureMessageEncodingMethod_DSA@2@XZ
 * Address: 0x140589FA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

__int64 __fastcall CryptoPP::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>,0>::Ref(__int64 a1)
{
  __int64 v1; // rax@6
  __int64 v3; // [sp+50h] [bp+8h]@1

  v3 = a1;
  if ( !(dword_184A8A360 & 1) )
  {
    dword_184A8A360 |= 1u;
    CryptoPP::simple_ptr<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>::simple_ptr<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>(&qword_184A8A358);
    atexit(sub_1406E9990);
  }
  while ( byte_184A8A354 )
  {
    if ( byte_184A8A354 != 1 )
      return qword_184A8A358;
  }
  byte_184A8A354 = 1;
  LODWORD(v1) = CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>::operator()(v3);
  qword_184A8A358 = v1;
  byte_184A8A354 = 2;
  return qword_184A8A358;
}
