﻿/*
 * Function: ?RecvThread@CNetProcess@@CAXPEAX@Z
 * Address: 0x140478340
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CNetProcess::RecvThread(void *pv)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@7
  int v4; // eax@13
  __int64 v5; // [sp+0h] [bp-B8h]@1
  DWORD *v6; // [sp+30h] [bp-88h]@4
  DWORD *v7; // [sp+38h] [bp-80h]@4
  CNetProcess *v8; // [sp+40h] [bp-78h]@4
  CNetSocket *v9; // [sp+48h] [bp-70h]@4
  unsigned int pdwOutIndex; // [sp+54h] [bp-64h]@6
  unsigned int dwAddSize; // [sp+74h] [bp-44h]@7
  int nBufMaxSize; // [sp+84h] [bp-34h]@7
  unsigned int v13; // [sp+88h] [bp-30h]@15
  _NET_BUFFER *v14; // [sp+90h] [bp-28h]@4
  _socket *v15; // [sp+98h] [bp-20h]@4
  int v16; // [sp+A0h] [bp-18h]@4
  bool v17; // [sp+A4h] [bp-14h]@7
  DWORD *v18; // [sp+C0h] [bp+8h]@1

  v18 = pv;
  v1 = &v5;
  for ( i = 44LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = v18;
  v7 = v18;
  v8 = (CNetProcess *)*((_QWORD *)v18 + 1);
  v9 = &v8->m_NetSocket;
  v14 = 0LL;
  v15 = 0LL;
  v16 = 0;
  while ( *v7 )
  {
    while ( CNetIndexList::PopNode_Front(&v8->m_listRecvEvent, &pdwOutIndex) )
    {
      CNetCriticalSection::Lock(&v8->m_csRecv[pdwOutIndex]);
      v14 = &v8->m_pRecvBuffer[pdwOutIndex];
      v15 = CNetSocket::GetSocket(v9, pdwOutIndex);
      nBufMaxSize = v14->m_nEtrSize + v14->m_nMaxSize - v14->m_dwPushPnt;
      v3 = _NET_BUFFER::GetPushPos(v14);
      v17 = CNetSocket::Recv(v9, pdwOutIndex, v3, nBufMaxSize, (int *)&dwAddSize);
      if ( !v17 || (signed int)dwAddSize <= 0 )
      {
        if ( dwAddSize != 10035 )
        {
          CNetProcess::PushCloseNode(v8, pdwOutIndex);
          if ( v8->m_Type.m_bSvrToS )
            CLogFile::Write(&v8->m_LogFile[2], "recv(%d).. ¿À·ù.. code(%d)", pdwOutIndex, dwAddSize);
        }
        goto LABEL_18;
      }
      v4 = _NET_BUFFER::GetLeftLoadSize(v14);
      if ( dwAddSize + v4 <= v14->m_nMaxSize )
      {
        v13 = v14->m_nMaxSize - v14->m_dwPushPnt;
        if ( (signed int)v13 < (signed int)dwAddSize )
          memcpy_0(v14->m_sMainBuffer, &v14->m_sMainBuffer[v14->m_nMaxSize], (signed int)(dwAddSize - v13));
        _NET_BUFFER::AddPushPos(v14, dwAddSize);
LABEL_18:
        CNetCriticalSection::Unlock(&v8->m_csRecv[pdwOutIndex]);
        Sleep(0);
      }
      else
      {
        CNetCriticalSection::Unlock(&v8->m_csRecv[pdwOutIndex]);
        CLogFile::Write(&v8->m_LogFile[2], "%d) RecvThread(%d).. ¸®½ºÆ®°ø°£¹ÌÈ®º¸..)", v8->m_nIndex, pdwOutIndex);
        CNetProcess::PushCloseNode(v8, pdwOutIndex);
      }
    }
    if ( ++v16 > v8->m_Type.m_byRecvSleepTime )
    {
      Sleep(1u);
      v16 = 0;
    }
  }
  _endthreadex(0);
}

