﻿/*
 * Function: ?release@?$auto_ptr@VPK_MessageAccumulatorBase@CryptoPP@@@std@@QEAAPEAVPK_MessageAccumulatorBase@CryptoPP@@XZ
 * Address: 0x14056C6D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

__int64 __fastcall std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::release(__int64 *a1)
{
  __int64 v1; // ST00_8@1

  v1 = *a1;
  *a1 = 0i64;
  return v1;
}
