﻿/*
 * Function: ?ProcessMessage@CMsgProcess@@EEAAXPEAU_message@@@Z
 * Address: 0x1401BFFD0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CMsgProcess::ProcessMessage(CMsgProcess *this, _message *pMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@9
  __int64 v5; // [sp+0h] [bp-48h]@1
  __int64 v6; // [sp+20h] [bp-28h]@4
  __int64 v7; // [sp+28h] [bp-20h]@4
  CMapData *pMap; // [sp+30h] [bp-18h]@9
  unsigned int v9; // [sp+38h] [bp-10h]@4
  _message *v10; // [sp+58h] [bp+10h]@1

  v10 = pMsg;
  v2 = &v5;
  for ( i = 16LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0LL;
  v7 = 0LL;
  v9 = _message::GetMessageA(pMsg);
  if ( v9 <= 0x2711 )
  {
    if ( v9 == 10001 )
    {
      CMapDisplay::OffDisplay(&g_MapDisplay);
      SendMessageA(g_pDoc->m_pwndMainFrame->m_hWnd, 0x10u, 0LL, 0LL);
    }
    else
    {
      v9 -= 1001;
      switch ( v9 )
      {
        case 0u:
          CMainThread::gm_DisplaymodeChange(&g_Main);
          break;
        case 1u:
          v4 = _message::GetKey1(v10);
          pMap = CMapOperation::GetMap(&g_MapOper, v4);
          CMainThread::gm_MapChange(&g_Main, pMap);
          break;
        case 2u:
          CMainThread::gm_MonsterInit(&g_Main, 0LL);
          break;
        case 3u:
          CMainThread::gm_ObjectSelect(&g_Main);
          break;
        case 4u:
          CMainThread::gm_UpdateServer(&g_Main);
          break;
        case 5u:
          CMainThread::gm_UpdateObject(&g_Main);
          break;
        case 6u:
          CMainThread::gm_UpdateMap(&g_Main);
          break;
        case 8u:
          CMainThread::gm_DisplayAll(&g_Main);
          break;
        case 9u:
          CMainThread::gm_PreCloseAnn(&g_Main);
          break;
        case 0xBu:
          CMainThread::gm_ServerClose(&g_Main);
          break;
        case 0xCu:
          CMainThread::gm_UserExit(&g_Main);
          break;
        case 0xDu:
          CMainThread::gm_MainThreadControl(&g_Main);
          break;
        case 0xEu:
          CMainThread::gm_DungeonLoad(&g_Main);
          break;
        default:
          return;
      }
    }
  }
}

