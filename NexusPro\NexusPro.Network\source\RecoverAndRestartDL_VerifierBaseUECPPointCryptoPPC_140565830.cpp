﻿/*
 * Function: ?RecoverAndRestart@?$DL_VerifierBase@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUDecodingResult@2@PEAEAEAVPK_MessageAccumulator@2@@Z
 * Address: 0x140565830
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

__int64  CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>::RecoverAndRestart(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  CryptoPP::CryptoMaterial *v4; // rax@1
  __int64 *v5; // rax@1
  __int64 v6; // rax@1
  unsigned __int64 v7; // rax@1
  __int64 *v8; // rax@1
  __int64 v9; // rax@1
  const void *v10; // rax@1
  __int64 v11; // rax@1
  CryptoPP *v12; // rcx@1
  struct CryptoPP::RandomNumberGenerator *v13; // rax@1
  char v14; // ST30_1@1
  char *v15; // rax@1
  int v16; // eax@1
  char *v17; // rax@1
  CryptoPP::Integer *v18; // rax@1
  __int64 *v19; // rax@1
  const void *v20; // rax@1
  __int64 v21; // rax@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v23; // [sp+50h] [bp-1D8h]@1
  __int64 v24; // [sp+68h] [bp-1C0h]@1
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v25; // [sp+70h] [bp-1B8h]@1
  __int64 v26; // [sp+78h] [bp-1B0h]@1
  __int64 *v27; // [sp+80h] [bp-1A8h]@1
  CryptoPP::Integer v28; // [sp+88h] [bp-1A0h]@1
  CryptoPP::Integer v29; // [sp+B0h] [bp-178h]@1
  char v30; // [sp+D8h] [bp-150h]@1
  CryptoPP::Integer v31; // [sp+E8h] [bp-140h]@1
  char v32; // [sp+110h] [bp-118h]@1
  char v33; // [sp+120h] [bp-108h]@1
  char v34; // [sp+130h] [bp-F8h]@1
  __int64 v35; // [sp+140h] [bp-E8h]@1
  __int64 v36; // [sp+148h] [bp-E0h]@1
  __int64 v37; // [sp+150h] [bp-D8h]@1
  __int64 *v38; // [sp+158h] [bp-D0h]@1
  __int64 v39; // [sp+160h] [bp-C8h]@1
  char *v40; // [sp+168h] [bp-C0h]@1
  __int64 v41; // [sp+170h] [bp-B8h]@1
  __int64 v42; // [sp+178h] [bp-B0h]@1
  unsigned __int64 v43; // [sp+180h] [bp-A8h]@1
  char *v44; // [sp+188h] [bp-A0h]@1
  __int64 v45; // [sp+190h] [bp-98h]@1
  unsigned __int64 v46; // [sp+198h] [bp-90h]@1
  unsigned __int64 v47; // [sp+1A0h] [bp-88h]@1
  unsigned __int64 v48; // [sp+1A8h] [bp-80h]@1
  unsigned __int8 *v49; // [sp+1B0h] [bp-78h]@1
  __int64 v50; // [sp+1B8h] [bp-70h]@1
  CryptoPP::Integer *v51; // [sp+1C0h] [bp-68h]@1
  CryptoPP::Integer *v52; // [sp+1C8h] [bp-60h]@1
  __int64 *v53; // [sp+1D0h] [bp-58h]@1
  unsigned __int64 v54; // [sp+1D8h] [bp-50h]@1
  char *v55; // [sp+1E0h] [bp-48h]@1
  unsigned __int64 v56; // [sp+1E8h] [bp-40h]@1
  char *v57; // [sp+1F0h] [bp-38h]@1
  __int64 v58; // [sp+1F8h] [bp-30h]@1
  __int64 v59; // [sp+200h] [bp-28h]@1
  __int64 v60; // [sp+230h] [bp+8h]@1
  __int64 v61; // [sp+238h] [bp+10h]@1
  __int64 v62; // [sp+248h] [bp+20h]@1

  v62 = a4;
  v61 = a2;
  v60 = a1;
  v35 = -2LL;
  v36 = *(_QWORD *)(a1 + 8);
  LODWORD(v4) = (*(int ( **)(signed __int64))(v36 + 32))(a1 + 8);
  CryptoPP::CryptoMaterial::DoQuickSanityCheck(v4);
  v24 = v62;
  LODWORD(v5) = (*(int ( **)(__int64))(*(_QWORD *)v60 + 136LL))(v60);
  v27 = v5;
  v25 = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> > *)(v60 + 16));
  v37 = *(_QWORD *)(v60 + 16);
  LODWORD(v6) = (*(int ( **)(signed __int64))(v37 + 8))(v60 + 16);
  v26 = v6;
  LODWORD(v7) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::MessageRepresentativeLength(v60);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v23,
    v7);
  LODWORD(v8) = (*(int ( **)(__int64))(*(_QWORD *)v60 + 144LL))(v60);
  v38 = v8;
  LODWORD(v9) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::MessageRepresentativeBitLength(v60);
  v39 = v9;
  v40 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v23);
  v41 = *(_QWORD *)v60;
  LODWORD(v10) = (*(int ( **)(__int64, char *))(v41 + 152))(v60, &v30);
  qmemcpy(&v33, v10, 0x10ui64);
  LODWORD(v11) = (*(int ( **)(__int64))(*(_QWORD *)v24 + 144LL))(v24);
  v42 = v11;
  v43 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 8));
  v44 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 8));
  v13 = CryptoPP::NullRNG(v12);
  v45 = *v38;
  v14 = *(_BYTE *)(v24 + 184);
  (*(void ( **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))(v45 + 48))(
    v38,
    v13,
    v44,
    v43);
  *(_BYTE *)(v24 + 184) = 1;
  v46 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v23);
  v15 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v23);
  CryptoPP::Integer::Integer(&v29, (const unsigned __int8 *)v15, v46, 0);
  v16 = ((int ( *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, _QWORD))v25->vfptr[12].__vecDelDtor)(
          v25,
          0LL);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::New(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 56),
    (unsigned int)v16);
  v47 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 80));
  v17 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 80));
  CryptoPP::Integer::Integer(&v28, (const unsigned __int8 *)v17, v47, 0);
  v48 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 56));
  v49 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 56));
  v50 = *v27;
  LODWORD(v18) = (*(int ( **)(__int64 *, CryptoPP::Integer *, CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, __int64))(v50 + 16))(
                   v27,
                   &v31,
                   v25,
                   v26);
  v51 = v18;
  v52 = v18;
  CryptoPP::Integer::Encode(v18, v49, v48, 0);
  CryptoPP::Integer::~Integer(&v31);
  LODWORD(v19) = (*(int ( **)(__int64))(*(_QWORD *)v60 + 144LL))(v60);
  v53 = v19;
  v54 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 80));
  v55 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 80));
  v56 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 56));
  v57 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v24 + 56));
  v58 = *(_QWORD *)v60;
  LODWORD(v20) = (*(int ( **)(__int64, char *))(v58 + 152))(v60, &v32);
  qmemcpy(&v34, v20, 0x10ui64);
  LODWORD(v21) = (*(int ( **)(__int64))(*(_QWORD *)v24 + 144LL))(v24);
  v59 = *v53;
  (*(void ( **)(__int64 *, __int64, __int64, char *))(v59 + 72))(v53, v61, v21, &v34);
  CryptoPP::Integer::~Integer(&v28);
  CryptoPP::Integer::~Integer(&v29);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v23);
  return v61;
}

