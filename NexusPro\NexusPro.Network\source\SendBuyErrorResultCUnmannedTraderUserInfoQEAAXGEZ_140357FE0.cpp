﻿/*
 * Function: ?SendBuyErrorResult@CUnmannedTraderUserInfo@@QEAAXGE@Z
 * Address: 0x140357FE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CUnmannedTraderUserInfo::SendBuyErrorResult(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, char byRet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-168h]@1
  _unmannedtrader_buy_item_result_zocl v7; // [sp+40h] [bp-128h]@4
  char pbyType; // [sp+144h] [bp-24h]@4
  char v9; // [sp+145h] [bp-23h]@4
  unsigned __int16 v10; // [sp+178h] [bp+10h]@1
  char v11; // [sp+180h] [bp+18h]@1

  v11 = byRet;
  v10 = wInx;
  v3 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _unmannedtrader_buy_item_result_zocl::_unmannedtrader_buy_item_result_zocl(&v7);
  v7.byRetCode = v11;
  pbyType = 30;
  v9 = 31;
  v5 = _unmannedtrader_buy_item_result_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10, &pbyType, &v7.byRetCode, v5);
}
