﻿/*
 * Function: ?ProcessRecoverableMessage@PK_SignatureMessageEncodingMethod@CryptoPP@@UEBAXAEAVHashTransformation@2@PEBE_K12AEAV?$SecBlock@EV?$AllocatorWithCleanup@E$0A@@CryptoPP@@@2@@Z
 * Address: 0x14058F910
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

const char * CryptoPP::PK_SignatureMessageEncodingMethod::ProcessRecoverableMessage(__int64 a1)
{
  const char *result; // rax@1

  result = (const char *)(unsigned __int8)(*(int (**)(void))(*(_QWORD *)a1 + 24LL))();
  if ( (_BYTE)result )
  {
    result = "ProcessRecoverableMessage() not implemented";
    if ( "ProcessRecoverableMessage() not implemented" )
      _wassert(
        L"!\"ProcessRecoverableMessage() not implemented\"",
        L"d:\\rf project\\rf_server64\\28 crypto++\\pubkey.h",
        0xD3u);
  }
  return result;
}

