﻿/*
 * Function: ?_TrapReturn@CPlayer@@QEAAXPEAVCTrap@@G@Z
 * Address: 0x1400A1650
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::_TrapReturn(CPlayer *this, CTrap *pTrap, unsigned __int16 wAddSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-C8h]@1
  bool bAdd[8]; // [sp+20h] [bp-A8h]@28
  char byCreateCode; // [sp+28h] [bp-A0h]@28
  CMapData *pMap; // [sp+30h] [bp-98h]@15
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-90h]@15
  float *pStdPos; // [sp+40h] [bp-88h]@15
  bool bHide; // [sp+48h] [bp-80h]@15
  int nTableCode; // [sp+50h] [bp-78h]@5
  unsigned __int16 *v13; // [sp+58h] [bp-70h]@6
  _STORAGE_LIST::_db_con pItem; // [sp+68h] [bp-60h]@8
  _STORAGE_LIST::_db_con *v15; // [sp+A8h] [bp-20h]@18
  char v16; // [sp+B0h] [bp-18h]@26
  unsigned int dwDur; // [sp+B4h] [bp-14h]@28
  CPlayer *pOwner; // [sp+D0h] [bp+8h]@1
  CTrap *v19; // [sp+D8h] [bp+10h]@1
  unsigned __int16 v20; // [sp+E0h] [bp+18h]@1

  v20 = wAddSerial;
  v19 = pTrap;
  pOwner = this;
  v3 = &v5;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pTrap )
  {
    nTableCode = GetItemTableCode(pTrap->m_pRecordSet->m_strCode);
    if ( nTableCode != -1 )
    {
      v13 = (unsigned __int16 *)CRecordData::GetRecord(
                                  (CRecordData *)&unk_1799C6AA0 + nTableCode,
                                  v19->m_pRecordSet->m_strCode);
      if ( v13 )
      {
        if ( v20 == 0xFFFF )
        {
          _STORAGE_LIST::_db_con::_db_con(&pItem);
          pItem.m_byTableCode = nTableCode;
          pItem.m_wItemIndex = *v13;
          if ( IsOverLapItem(nTableCode) )
            pItem.m_dwDur = 1i64;
          else
            pItem.m_dwDur = GetItemDurPoint(nTableCode, *(_DWORD *)v13);
          if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum) == 255 )
          {
            bHide = 0;
            pStdPos = pOwner->m_fCurPos;
            wLayerIndex = pOwner->m_wMapLayerIndex;
            pMap = pOwner->m_pCurMap;
            CreateItemBox(&pItem, pOwner, 0xFFFFFFFF, 0, 0i64, 1, pMap, wLayerIndex, pOwner->m_fCurPos, 0);
            CMgrAvatorItemHistory::back_trap_item(
              &CPlayer::s_MgrItemHistory,
              pOwner->m_ObjID.m_wIndex,
              &pItem,
              pOwner->m_szItemHistoryFileName);
          }
          else
          {
            pItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pOwner->m_Param);
            if ( CPlayer::Emb_AddStorage(pOwner, 0, (_STORAGE_LIST::_storage_con *)&pItem.m_bLoad, 0, 1) )
            {
              CPlayer::SendMsg_RewardAddItem(pOwner, &pItem, 6);
              CMgrAvatorItemHistory::back_trap_item(
                &CPlayer::s_MgrItemHistory,
                pOwner->m_ObjID.m_wIndex,
                &pItem,
                pOwner->m_szItemHistoryFileName);
            }
            else
            {
              CMgrAvatorItemHistory::add_storage_fail(
                &CPlayer::s_MgrItemHistory,
                pOwner->m_ObjID.m_wIndex,
                &pItem,
                "_TrapDestroy - Emb_AddStorage() Fail",
                pOwner->m_szItemHistoryFileName);
            }
          }
        }
        else if ( IsOverLapItem(nTableCode) )
        {
          v15 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum, v20);
          if ( v15 )
          {
            if ( !v15->m_bLock && v15->m_byTableCode == nTableCode && v15->m_wItemIndex == *(_DWORD *)v13 )
            {
              if ( v15->m_dwDur + 1 <= 0x63 )
              {
                v16 = 1;
                if ( !IsProtectItem(nTableCode) )
                  v16 = 0;
                byCreateCode = 0;
                bAdd[0] = 1;
                dwDur = CPlayer::Emb_AlterDurPoint(pOwner, 0, v15->m_byStorageIndex, 1, 1, 0);
                CPlayer::SendMsg_AdjustAmountInform(pOwner, 0, v20, dwDur);
              }
              else
              {
                CPlayer::SendMsg_AdjustAmountInform(pOwner, 0, v20, v15->m_dwDur);
              }
            }
          }
          else
          {
            CPlayer::SendMsg_AdjustAmountInform(pOwner, 0, v20, 0);
          }
        }
      }
    }
  }
}

