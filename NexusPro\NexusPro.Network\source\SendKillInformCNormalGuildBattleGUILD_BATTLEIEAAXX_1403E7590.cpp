﻿/*
 * Function: ?SendKillInform@CNormalGuildBattle@GUILD_BATTLE@@IEAAXXZ
 * Address: 0x1403E7590
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  GUILD_BATTLE::CNormalGuildBattle::SendKillInform(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleSchedulePool *v3; // rax@7
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-78h]@1
  char byType; // [sp+24h] [bp-54h]@4
  char v7; // [sp+25h] [bp-53h]@4
  unsigned int Dst; // [sp+48h] [bp-30h]@4
  unsigned int v9; // [sp+4Ch] [bp-2Ch]@6
  char byHour; // [sp+50h] [bp-28h]@9
  char byMin; // [sp+51h] [bp-27h]@9
  char bySec; // [sp+52h] [bp-26h]@9
  GUILD_BATTLE::CGuildBattleSchedule *v13; // [sp+68h] [bp-10h]@7
  GUILD_BATTLE::CNormalGuildBattle *v14; // [sp+80h] [bp+8h]@1

  v14 = this;
  v1 = &v5;
  for ( i = 28LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  byType = 27;
  v7 = 75;
  memset_0(&Dst, 0, 0xBui64);
  if ( v14->m_pkRed && v14->m_pkBlue )
  {
    Dst = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v14->m_pkRed);
    v9 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v14->m_pkBlue);
  }
  v3 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
  v13 = GUILD_BATTLE::CGuildBattleSchedulePool::GetRef(v3, v14->m_dwID);
  if ( v13 )
  {
    GUILD_BATTLE::CGuildBattleSchedule::GetLeftTime(v13, &byHour, &byMin, &bySec);
    GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v14->m_k1P, &byType, (char *)&Dst, 0xBu);
    GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v14->m_k2P, &byType, (char *)&Dst, 0xBu);
  }
  else
  {
    v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v4,
      "CNormalGuildBattle::SendKillInform() : CGuildBattleSchedulePool::Instance()->GetRef(%u) NULL!",
      v14->m_dwID);
  }
}

