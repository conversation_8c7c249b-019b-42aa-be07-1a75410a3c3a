﻿/*
 * Function: ?Send@CReservedGuildSchedulePage@GUILD_BATTLE@@QEAAXHKPEAV12@@Z
 * Address: 0x1403CBE20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall GUILD_BATTLE::CReservedGuildSchedulePage::Send(GUILD_BATTLE::CReservedGuildSchedulePage *this, int n, unsigned int dwVer, GUILD_BATTLE::CReservedGuildSchedulePage *pkSelfInfoPage)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-98h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-78h]@13
  char szMsg[4]; // [sp+34h] [bp-64h]@6
  char pbyType; // [sp+54h] [bp-44h]@6
  char v10; // [sp+55h] [bp-43h]@6
  char *Source; // [sp+68h] [bp-30h]@9
  char v12; // [sp+74h] [bp-24h]@13
  char v13; // [sp+75h] [bp-23h]@13
  GUILD_BATTLE::CReservedGuildSchedulePage *v14; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v14 = this;
  v4 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v14->m_pkList )
  {
    if ( v14->m_dwVer == dwVer )
    {
      *(_DWORD *)szMsg = v14->m_dwVer;
      pbyType = 27;
      v10 = 59;
      CNetProcess::LoadSendMsg(unk_1414F2088, n, &pbyType, szMsg, 4u);
    }
    else
    {
      if ( pkSelfInfoPage )
      {
        if ( pkSelfInfoPage->m_pkList->bySelfScheduleInx >= 5 )
        {
          v14->m_pkList->bySelfScheduleInx = 0;
        }
        else
        {
          Source = pkSelfInfoPage->m_pkList->List[pkSelfInfoPage->m_pkList->bySelfScheduleInx].wsz1PName;
          strcpy_0(v14->m_pkList->List[4].wsz1PName, Source);
          v14->m_pkList->List[4].by1PRace = Source[17];
          strcpy_0(v14->m_pkList->List[4].wsz2PName, Source + 18);
          v14->m_pkList->List[4].by2PRace = Source[35];
          v14->m_pkList->List[4].byStartHour = Source[36];
          v14->m_pkList->List[4].byStartMin = Source[37];
          v14->m_pkList->List[4].byEndHour = Source[38];
          v14->m_pkList->List[4].byEndMin = Source[39];
          v14->m_pkList->bySelfScheduleInx = 1;
        }
      }
      else
      {
        v14->m_pkList->bySelfScheduleInx = 0;
      }
      v12 = 27;
      v13 = 61;
      nLen = _guild_battle_reserved_schedule_result_zocl::size(v14->m_pkList);
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &v12, (char *)v14->m_pkList, nLen);
    }
  }
}
