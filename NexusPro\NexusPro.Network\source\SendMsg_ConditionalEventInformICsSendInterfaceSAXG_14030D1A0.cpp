﻿/*
 * Function: ?SendMsg_ConditionalEventInform@ICsSendInterface@@SAXGEGEPEAD@Z
 * Address: 0x14030D1A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall ICsSendInterface::SendMsg_ConditionalEventInform(unsigned __int16 wSock, char byEventType, unsigned __int16 wCsDiscount, char byStatus, char *pEMsg)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@4
  unsigned __int16 v8; // ax@4
  __int64 v9; // [sp+0h] [bp-98h]@1
  _conditional_event_inform_zocl Dst; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v12; // [sp+65h] [bp-33h]@4
  unsigned __int64 v13; // [sp+80h] [bp-18h]@4
  unsigned __int16 v14; // [sp+A0h] [bp+8h]@1
  char v15; // [sp+A8h] [bp+10h]@1
  unsigned __int16 v16; // [sp+B0h] [bp+18h]@1
  char v17; // [sp+B8h] [bp+20h]@1

  v17 = byStatus;
  v16 = wCsDiscount;
  v15 = byEventType;
  v14 = wSock;
  v5 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = (unsigned __int64)&v9 ^ _security_cookie;
  v7 = _conditional_event_inform_zocl::size(&Dst);
  memset_0(&Dst, 0, v7);
  Dst.byEventType = v15;
  Dst.wCsDiscount = v16;
  Dst.byEventStatus = v17;
  strcpy_0(Dst.szMsgCode, pEMsg);
  pbyType = 57;
  v12 = 9;
  v8 = _conditional_event_inform_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v14, &pbyType, &Dst.byEventType, v8);
}
