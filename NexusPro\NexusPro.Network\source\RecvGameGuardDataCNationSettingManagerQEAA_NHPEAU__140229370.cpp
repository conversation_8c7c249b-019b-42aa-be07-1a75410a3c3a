﻿/*
 * Function: ?RecvGameGuardData@CNationSettingManager@@_NHPEAU_MSG_HEADER@@PEAD@Z
 * Address: 0x140229370
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool  CNationSettingManager::RecvGameGuardData(CNationSettingManager *this, int n, _MSG_HEADER *pHeader, char *pBuff)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  INationGameGuardSystem *v8; // [sp+20h] [bp-18h]@5
  CNationSettingManager *v9; // [sp+40h] [bp+8h]@1
  int v10; // [sp+48h] [bp+10h]@1
  _MSG_HEADER *v11; // [sp+50h] [bp+18h]@1
  char *v12; // [sp+58h] [bp+20h]@1

  v12 = pBuff;
  v11 = pHeader;
  v10 = n;
  v9 = this;
  v4 = &v7;
  for ( i = 12LL; i; --i )
  {
    *(DWORD *)v4 = 0xCCCCCCCC;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CNationSettingData::GetGameGuardSystem(v9->m_pData) )
  {
    v8 = CNationSettingData::GetGameGuardSystem(v9->m_pData);
    result = ((int ( *)(INationGameGuardSystem *, _QWORD, _MSG_HEADER *, char *))v8->vfptr->RecvClientLine)(
               v8,
               (unsigned int)v10,
               v11,
               v12);
  }
  else
  {
    result = 0;
  }
  return result;
}

