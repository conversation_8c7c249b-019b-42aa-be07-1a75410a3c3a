﻿/*
 * Function: ?SendMsg_CouponError@CCouponMgr@@QEAAXGE@Z
 * Address: 0x1403FE110
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CCouponMgr::SendMsg_CouponError(CCouponMgr *this, unsigned __int16 wIdx, char byRet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-78h]@1
  _notify_coupon_error_zocl v7; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  unsigned __int16 v10; // [sp+88h] [bp+10h]@1

  v10 = wIdx;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.byRetCode = byRet;
  pbyType = 59;
  v9 = 8;
  v5 = _notify_coupon_error_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10, &pbyType, &v7.byRetCode, v5);
}
