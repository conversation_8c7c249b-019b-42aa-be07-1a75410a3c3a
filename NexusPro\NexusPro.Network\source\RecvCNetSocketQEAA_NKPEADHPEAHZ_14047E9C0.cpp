﻿/*
 * Function: ?Recv@CNetSocket@@_NKPEADHPEAH@Z
 * Address: 0x14047E9C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

char  CNetSocket::Recv(CNetSocket *this, unsigned int n, char *pBuf, int nBufMaxSize, int *pnRet)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v8; // [sp+0h] [bp-28h]@1
  CNetSocket *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v5 = &v8;
  for ( i = 8LL; i; --i )
  {
    *(DWORD *)v5 = 0xCCCCCCCC;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  *pnRet = recv(v9->m_Socket[n].m_Socket, pBuf, nBufMaxSize, 0);
  if ( *pnRet == -1 )
  {
    *pnRet = WSAGetLastError();
    if ( *pnRet != 10035 )
      ++v9->m_TotalCount.m_dwTotalRecvErrNum;
    result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}

