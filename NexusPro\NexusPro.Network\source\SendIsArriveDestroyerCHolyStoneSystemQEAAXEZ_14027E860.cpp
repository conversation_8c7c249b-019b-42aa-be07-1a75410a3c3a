﻿/*
 * Function: ?SendIsArriveDestroyer@CHolyStoneSystem@@XE@Z
 * Address: 0x14027E860
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CHolyStoneSystem::SendIsArriveDestroyer(CHolyStoneSystem *this, char byArrive)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@5
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@5
  char v7; // [sp+39h] [bp-5Fh]@5
  char Dst; // [sp+3Ah] [bp-5Eh]@5
  char v9; // [sp+4Ah] [bp-4Eh]@5
  unsigned int v10; // [sp+4Bh] [bp-4Dh]@5
  char pbyType; // [sp+64h] [bp-34h]@5
  char v12; // [sp+65h] [bp-33h]@5
  unsigned int dwClientIndex; // [sp+74h] [bp-24h]@5
  unsigned __int64 v14; // [sp+80h] [bp-18h]@4
  CHolyStoneSystem *v15; // [sp+A0h] [bp+8h]@1

  v15 = this;
  v2 = &v5;
  for ( i = 36LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v15->m_pkDestroyer )
  {
    szMsg = byArrive;
    v7 = CPlayerDB::GetRaceCode(&v15->m_pkDestroyer->m_Param);
    v4 = CPlayerDB::GetCharNameW(&v15->m_pkDestroyer->m_Param);
    memcpy_0(&Dst, v4, 0x10ui64);
    v9 = 0;
    v10 = v15->m_SaveData.m_dwDestroyerSerial;
    pbyType = 25;
    v12 = 17;
    for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
    {
      if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
        CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 0x17u);
    }
  }
}

