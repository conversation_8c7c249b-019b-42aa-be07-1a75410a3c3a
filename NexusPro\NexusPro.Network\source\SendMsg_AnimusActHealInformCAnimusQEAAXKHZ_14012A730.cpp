﻿/*
 * Function: ?SendMsg_AnimusActHealInform@CAnimus@@XKH@Z
 * Address: 0x14012A730
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CAnimus::SendMsg_AnimusActHealInform(CAnimus *this, unsigned int dwDstSerial, int nAddHP)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  unsigned int v7; // [sp+3Ch] [bp-4Ch]@4
  __int16 v8; // [sp+40h] [bp-48h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v10; // [sp+65h] [bp-23h]@4
  CAnimus *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v3 = &v5;
  for ( i = 32LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *(DWORD *)szMsg = v11->m_dwObjSerial;
  v7 = dwDstSerial;
  v8 = nAddHP;
  pbyType = 22;
  v10 = 14;
  CGameObject::CircleReport((CGameObject *)&v11->vfptr, &pbyType, szMsg, 10, 0);
}

