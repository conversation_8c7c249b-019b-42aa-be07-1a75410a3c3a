﻿/*
 * Function: ?SendMsg_CashEventInform@ICsSendInterface@@SAXGEEPEAU_cash_event_ini@@PEAU_cash_lim_sale@@@Z
 * Address: 0x14030CE00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall ICsSendInterface::SendMsg_CashEventInform(unsigned __int16 wSock, char byEventType, char byStatus, _cash_event_ini *pIni, _cash_lim_sale *pLim)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v7; // ax@21
  __int64 v8; // [sp+0h] [bp-158h]@1
  _cash_event_inform_zocl v9; // [sp+40h] [bp-118h]@4
  unsigned __int8 v10; // [sp+E4h] [bp-74h]@12
  int j; // [sp+E8h] [bp-70h]@12
  unsigned __int16 Dst; // [sp+F8h] [bp-60h]@15
  char v13; // [sp+FAh] [bp-5Eh]@15
  char v14; // [sp+FEh] [bp-5Ah]@15
  char v15; // [sp+100h] [bp-58h]@15
  char v16; // [sp+102h] [bp-56h]@15
  int k; // [sp+114h] [bp-44h]@16
  char pbyType; // [sp+124h] [bp-34h]@21
  char v19; // [sp+125h] [bp-33h]@21
  unsigned __int64 v20; // [sp+140h] [bp-18h]@4
  unsigned __int16 v21; // [sp+160h] [bp+8h]@1
  char v22; // [sp+168h] [bp+10h]@1
  char v23; // [sp+170h] [bp+18h]@1
  _cash_event_ini *v24; // [sp+178h] [bp+20h]@1

  v24 = pIni;
  v23 = byStatus;
  v22 = byEventType;
  v21 = wSock;
  v5 = &v8;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v20 = (unsigned __int64)&v8 ^ _security_cookie;
  _cash_event_inform_zocl::_cash_event_inform_zocl(&v9);
  if ( (signed int)(unsigned __int8)v22 < 3 )
    v9.byEventType = v22;
  else
    v9.byEventType = 6;
  if ( (signed int)(unsigned __int8)v23 < 8 )
    v9.byEventStatus = v23;
  else
    v9.byEventType = 6;
  if ( v24 )
  {
    if ( v22 == 2 )
    {
      v10 = v24->m_byLimited_sale_num;
      v9.LimitedSale.byCount = v10;
      v9.LimitedSale.byDiscount = v24->m_byLimDiscout;
      for ( j = 0; j < v10; ++j )
      {
        v9.LimitedSale.item[j].byTableCode = pLim->m_EventItemInfo[j].byTableCode;
        v9.LimitedSale.item[j].dwIndex = pLim->m_EventItemInfo[j].dwIndex;
        v9.LimitedSale.item[j].wNum = pLim->m_EventItemInfo[j].wCount;
      }
      memset_0(&Dst, 0, 0x10ui64);
      GetLocalTime((LPSYSTEMTIME)&Dst);
      v9.wYear[0] = Dst;
      v9.byMonth[0] = v13;
      v9.byDay[0] = v14;
      v9.byHour[0] = v15;
      v9.byMinute[0] = v16;
      v9.wYear[1] = v24->m_wYear[1];
      v9.byMonth[1] = v24->m_byMonth[1];
      v9.byDay[1] = v24->m_byDay[1];
      v9.byHour[1] = v24->m_byHour[1];
      v9.byMinute[1] = v24->m_byMinute[1];
    }
    else
    {
      for ( k = 0; k < 2; ++k )
      {
        v9.wYear[k] = v24->m_wYear[k];
        v9.byMonth[k] = v24->m_byMonth[k];
        v9.byDay[k] = v24->m_byDay[k];
        v9.byHour[k] = v24->m_byHour[k];
        v9.byMinute[k] = v24->m_byMinute[k];
      }
    }
  }
  else
  {
    v9.byEventType = 6;
  }
  pbyType = 57;
  v19 = 8;
  v7 = _cash_event_inform_zocl::size(&v9);
  CNetProcess::LoadSendMsg(unk_1414F2088, v21, &pbyType, &v9.byEventType, v7);
}
