﻿/*
 * Function: ?_UpdateUnitDebt@CPlayer@@QEAAXEK@Z
 * Address: 0x140106970
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::_UpdateUnitDebt(CPlayer *this, char bySlotIndex, unsigned int dwPull)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  _UNIT_DB_BASE::_LIST *pData; // [sp+20h] [bp-18h]@5
  CPlayer *v7; // [sp+40h] [bp+8h]@1
  char v8; // [sp+48h] [bp+10h]@1

  v8 = bySlotIndex;
  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_pUserDB )
  {
    pData = &v7->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex];
    v7->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].nPullingFee = dwPull;
    CPlayer::SendMsg_UnitAlterFeeInform(v7, bySlotIndex, dwPull);
    CUserDB::Update_UnitData(v7->m_pUserDB, v8, pData);
  }
}

