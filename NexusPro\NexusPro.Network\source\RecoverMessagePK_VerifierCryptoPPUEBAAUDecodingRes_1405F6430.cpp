﻿/*
 * Function: ?RecoverMessage@PK_Verifier@CryptoPP@@UEBA?AUDecodingResult@2@PEAEPEBE_K12@Z
 * Address: 0x1405F6430
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

struct CryptoPP::DecodingResult * CryptoPP::PK_Verifier::RecoverMessage(CryptoPP::PK_Verifier *this, struct CryptoPP::DecodingResult *retstr, unsigned __int8 *a3, const unsigned __int8 *a4, unsigned __int64 a5, const unsigned __int8 *a6, unsigned __int64 a7)
{
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  __int64 v9; // rax@1
  __int64 v10; // rax@1
  char v12; // [sp+20h] [bp-38h]@1
  __int64 v13; // [sp+28h] [bp-30h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v14; // [sp+30h] [bp-28h]@1
  __int64 v15; // [sp+38h] [bp-20h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v16; // [sp+40h] [bp-18h]@1
  CryptoPP::PK_Verifier *v17; // [sp+60h] [bp+8h]@1
  struct CryptoPP::DecodingResult *v18; // [sp+68h] [bp+10h]@1
  unsigned __int8 *v19; // [sp+70h] [bp+18h]@1
  const unsigned __int8 *v20; // [sp+78h] [bp+20h]@1

  v20 = a4;
  v19 = a3;
  v18 = retstr;
  v17 = this;
  v13 = -2LL;
  LODWORD(v7) = ((int (*)(void))this->vfptr[1].__vecDelDtor)();
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::auto_ptr<CryptoPP::PK_MessageAccumulator>(&v12, v7);
  LODWORD(v8) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v12);
  v14 = v17->vfptr;
  (*(void ( **)(CryptoPP::PK_Verifier *, __int64, const unsigned __int8 *, unsigned __int64))&v14[1].gap8[0])(
    v17,
    v8,
    a6,
    a7);
  LODWORD(v9) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator->(&v12);
  v15 = v9;
  (*(void ( **)(__int64, const unsigned __int8 *, unsigned __int64))(*(_QWORD *)v9 + 24LL))(v9, v20, a5);
  LODWORD(v10) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v12);
  v16 = v17->vfptr;
  ((void ( *)(CryptoPP::PK_Verifier *, struct CryptoPP::DecodingResult *, unsigned __int8 *, __int64))v16[1].AllowNonrecoverablePart)(
    v17,
    v18,
    v19,
    v10);
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::~auto_ptr<CryptoPP::PK_MessageAccumulator>(&v12);
  return v18;
}

